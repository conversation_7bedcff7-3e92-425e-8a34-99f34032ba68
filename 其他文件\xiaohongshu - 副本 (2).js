/**
 * 小红书API操作模块
 * 包含API接口调用和互动元素获取功能
 * 
 * 功能列表：
 * 1. 设备注册API
 * 2. 获取链接API
 * 3. 更新操作状态API
 * 4. 设备令牌管理
 * 5. 操作信息管理
 * 6. 互动元素获取
 * 
 * 作者: Claude
 * 日期: 2024-07-12
 */

// 引入DeviceOperation模块，用于设备操作
var deviceOp = require('./DeviceOperation.js');

// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;

/**
 * API配置
 * 根据API调用说明文档配置API路径
 */
const API配置 = {
    基本URL: "http://xhss.ke999.cn",  // 移除"/api"前缀
    用户名: "admin", // 请在此处填写您的用户名
    设备令牌: "", // 如果为空，将自动生成
    设备名称: device.brand + " " + device.model // 自动获取设备名称
};

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    throw new Error("请在AutoJS环境中运行此脚本");
}

console.log("小红书API模块已加载");

/**
 * 生成随机设备令牌
 * 
 * @returns {string} - 随机生成的设备令牌
 */
function 生成设备令牌() {
    // 使用与网页测试一致的格式：device_时间戳_随机字符串
    const 时间戳 = Date.now();
    let 随机字符串 = "";
    const 可能字符 = "abcdefghijklmnopqrstuvwxyz0123456789";
    
    // 生成8位随机字符串
    for (let i = 0; i < 8; i++) {
        随机字符串 += 可能字符.charAt(Math.floor(Math.random() * 可能字符.length));
    }
    
    // 格式: device_时间戳_随机字符串
    return `device_${时间戳}_${随机字符串}`;
}

/**
 * 获取或初始化设备令牌
 * 每次启动都生成新的随机设备令牌
 *
 * @returns {string} - 设备令牌
 */
function 获取设备令牌() {
    // 如果已经设置了设备令牌，直接返回
    if (API配置.设备令牌) {
        return API配置.设备令牌;
    }

    // 每次启动都生成新的随机设备令牌，不再使用存储的固定令牌
    console.log("生成新的随机设备令牌");
    let 新令牌 = 生成设备令牌();
    API配置.设备令牌 = 新令牌;

    console.log("新生成的设备令牌: " + 新令牌);
    return 新令牌;
}

/**
 * 更新设备令牌（当达到每日限制时调用）
 * 
 * @returns {string} - 新的设备令牌
 */
function 更新设备令牌() {
    let 新令牌 = 生成设备令牌();
    API配置.设备令牌 = 新令牌;
    
    // 保存到存储
    storages.create("小红书操作").put("设备令牌", 新令牌);
    
    console.log("已更新设备令牌: " + 新令牌);
    return 新令牌;
}

/**
 * 发送HTTP请求
 * 
 * @param {string} 方法 - 请求方法，"GET"或"POST"
 * @param {string} 路径 - API路径
 * @param {Object} 数据 - 请求数据
 * @returns {Object} - 响应数据
 */
function 发送请求(方法, 路径, 数据) {
    let url = API配置.基本URL + 路径;
    
    console.log(`发送${方法}请求到: ${url}`);
    console.log(`请求数据: ${JSON.stringify(数据)}`);
    
    try {
        let response;
        
        if (方法 === "GET" && 数据) {
            // 构建查询字符串
            let 查询参数 = [];
            for (let key in 数据) {
                查询参数.push(encodeURIComponent(key) + "=" + encodeURIComponent(数据[key]));
            }
            url += "?" + 查询参数.join("&");
            console.log(`完整GET请求URL: ${url}`);
            response = http.get(url);
        } else if (方法 === "POST") {
            // 发送POST请求，不设置contentType
            response = http.post(url, 数据);
        } else {
            response = http.get(url);
        }
        
        // 记录响应状态码
        console.log(`响应状态码: ${response.statusCode}`);
        
        if (response.statusCode >= 200 && response.statusCode < 300) {
            let 响应内容 = response.body.json();
            console.log(`响应内容: ${JSON.stringify(响应内容)}`);
            return 响应内容;
        } else {
            // 详细记录错误信息
            let 错误信息 = `请求失败，状态码: ${response.statusCode}`;
            try {
                let 错误响应 = response.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return { 
                    success: false, 
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return { 
            success: false, 
            message: `发送请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 设备注册API
 * 
 * @param {string} 设备类型 - 可选，设备类型，默认为'mobile'
 * @param {boolean} 是否临时设备 - 可选，是否为临时设备，默认为false
 * @returns {Object} - 注册结果
 */
function 设备注册(设备类型 = 'mobile', 是否临时设备 = false) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;
    
    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }
    
    let 数据 = {
        username: 用户名,
        device_token: 设备令牌,
        device_name: API配置.设备名称,
        device_type: 设备类型,
        is_temp: 是否临时设备
    };
    
    console.log("发送设备注册请求: " + JSON.stringify(数据));
    
    try {
        // 构建URL - 使用与网页测试一致的路径
        let url = API配置.基本URL + "/device/register";
        console.log(`注册路径: ${url}`);
        
        // 使用POST请求，不设置contentType
        let 响应 = http.post(url, 数据);
        
        console.log(`注册响应状态码: ${响应.statusCode}`);
        
        if (响应.statusCode >= 200 && 响应.statusCode < 300) {
            let 结果 = 响应.body.json();
            console.log("设备注册结果: " + JSON.stringify(结果));
            
            // 保存操作次数信息和配置信息
            if (结果.success && 结果.data) {
                // 保存操作信息
                let 操作信息 = {
                    操作次数: 结果.data.operation_count || 0,
                    最大操作次数: 结果.data.max_operations || 0,
                    剩余操作次数: 结果.data.remaining_operations || 0
                };
                storages.create("小红书操作").put("操作信息", 操作信息);
                
                // 检查是否已达到每日操作上限
                if (操作信息.剩余操作次数 <= 0) {
                    console.log("当前设备今日操作次数已达上限，无法继续操作");
                    toast("今日操作次数已达上限，即将退出本次注册");
                    sleep(3000);
                    return { success: false, message: "今日操作次数已达上限", limitReached: true };
                }
                
                // 保存设备ID
                if (结果.data.device_id) {
                    storages.create("小红书操作").put("设备ID", 结果.data.device_id);
                }
                
                // 保存配置信息
                if (结果.data.configs && Array.isArray(结果.data.configs) && 结果.data.configs.length > 0) {
                    // 提取所有配置项
                    let 所有配置 = {};
                    
                    // 遍历所有配置
                    for (let i = 0; i < 结果.data.configs.length; i++) {
                        let 配置项 = 结果.data.configs[i];
                        
                        // 如果配置有name和config属性
                        if (配置项.name && 配置项.config) {
                            // 将配置添加到所有配置对象
                            所有配置[配置项.name] = 配置项.config;
                            
                            // 如果是默认配置，单独保存
                            if (配置项.is_default === 1) {
                                for (let 键 in 配置项.config) {
                                    console.log(`已保存默认配置: ${键}`);
                                    storages.create("小红书操作").put(键, 配置项.config[键]);
                                    
                                    // 特别处理"操作几次恢复出厂设置"，设置为全局变量
                                    if (键 === "操作几次恢复出厂设置") {
                                        // 设置为全局变量，方便其他模块访问
                                        global.操作几次恢复出厂设置 = 配置项.config[键];
                                        console.log(`已设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                                    }
                                }
                            }
                        }
                    }
                    
                    // 保存所有配置
                    storages.create("小红书操作").put("所有配置", 所有配置);
                    
                    // 寻找默认配置
                    let 默认配置 = 结果.data.configs.find(配置 => 配置.is_default === 1);
                    
                    // 如果没有默认配置，使用第一个配置
                    if (!默认配置 && 结果.data.configs.length > 0) {
                        默认配置 = 结果.data.configs[0];
                    }
                    
                    // 保存默认配置
                    if (默认配置) {
                        storages.create("小红书操作").put("当前配置", 默认配置);
                        
                        // 如果默认配置中有"操作几次恢复出厂设置"，设置为全局变量
                        if (默认配置.config && 默认配置.config.操作几次恢复出厂设置 !== undefined) {
                            global.操作几次恢复出厂设置 = 默认配置.config.操作几次恢复出厂设置;
                            console.log(`从默认配置设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                        }
                    }
                }
                
                // 输出设备令牌，便于调试
                console.log("设备注册成功，当前设备令牌: " + 设备令牌);
                console.log("当前操作信息: " + JSON.stringify(操作信息));
            }
            
            return 结果;
        } else {
            // 请求失败
            let 错误信息 = `注册请求失败，状态码: ${响应.statusCode}`;
            try {
                let 错误响应 = 响应.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return { 
                    success: false, 
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送注册请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return { 
            success: false, 
            message: `发送注册请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 获取下一个链接API
 * 
 * @returns {Object} - 链接数据
 */
function 获取下一个链接() {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;
    
    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }
    
    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌
    };
    
    let 结果 = 发送请求("GET", "/next-link", 数据);
    console.log("获取链接结果: " + JSON.stringify(结果));
    
    // 显示toast提示消息
    if (结果.toast && 结果.toast.message) {
        toast(结果.toast.message);
    } else if (结果.status === "no_links") {
        toast("当前没有可操作的链接，30秒后将重试");
    } else if (结果.status === "limit_reached") {
        toast("今日操作次数已达上限，即将退出脚本");
        console.log("达到每日操作上限，准备退出脚本");
        // 等待3秒，让用户看到提示
        sleep(3000);
        // 退出脚本
        exit();
    } else if (结果.success && 结果.status === "success") {
        toast("获取链接成功，正在处理...");
    }
    
    // 如果达到限制，更新设备令牌
    if (结果.status === "limit_reached") {
        console.log("已达到每日限制，更新设备令牌");
        更新设备令牌();
    }
    
    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: (结果.data.max_operations || 0) - (结果.data.operation_count || 0)
        };
        storages.create("小红书操作").put("操作信息", 操作信息);
        
        // 保存当前链接ID，供更新状态使用
        if (结果.data.link_id) {
            storages.create("小红书操作").put("当前链接ID", 结果.data.link_id);
            console.log("保存链接ID: " + 结果.data.link_id);
        }
    }
    
    return 结果;
}

/**
 * 更新操作状态API
 * 
 * @param {number} 链接ID - 链接ID
 * @param {string} 状态 - 操作状态: success(成功)或failed(失败)
 * @param {string} 操作类型 - 操作类型: like(点赞)、collect(收藏)或both(两者)
 * @param {number} 操作前点赞数 - 操作前的点赞数量
 * @param {number} 操作前收藏数 - 操作前的收藏数量
 * @param {string} 错误信息 - 错误信息(仅当status=failed时需要)
 * @returns {Object} - 更新结果
 */
function 更新操作状态(链接ID, 状态, 操作类型, 操作前点赞数, 操作前收藏数, 错误信息) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;
    
    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }
    
    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌,
        link_id: 链接ID,
        status: 状态,
        operation_type: 操作类型,
        before_like_count: 操作前点赞数,
        before_collect_count: 操作前收藏数
    };
    
    if (状态 === "failed" && 错误信息) {
        数据.error_message = 错误信息;
    }
    
    let 结果 = 发送请求("POST", "/update-status", 数据);
    console.log("更新状态结果: " + JSON.stringify(结果));
    
    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: 结果.data.remaining_operations || 0
        };
        storages.create("小红书操作").put("操作信息", 操作信息);
    }
    
    return 结果;
}

/**
 * 获取当前操作信息
 * 
 * @returns {Object} - 操作信息
 */
function 获取操作信息() {
    let 存储的信息 = storages.create("小红书操作").get("操作信息");
    if (!存储的信息) {
        return {
            操作次数: 0,
            最大操作次数: 0,
            剩余操作次数: 0
        };
    }
    
    // 确保剩余操作次数不是NaN
    if (isNaN(存储的信息.剩余操作次数)) {
        存储的信息.剩余操作次数 = 存储的信息.最大操作次数 - 存储的信息.操作次数;
        if (存储的信息.剩余操作次数 < 0) 存储的信息.剩余操作次数 = 0;
        storages.create("小红书操作").put("操作信息", 存储的信息);
    }
    
    return 存储的信息;
}

/**
 * 获取当前配置
 * 
 * @returns {Object|null} - 当前配置信息，如果没有则返回null
 */
function 获取当前配置() {
    let 当前配置 = storages.create("小红书操作").get("当前配置");
    if (!当前配置) {
        console.log("未找到当前配置");
        return null;
    }
    
    console.log("获取到当前配置: " + 当前配置.name);
    return 当前配置;
}

/**
 * 设置API配置
 * 
 * @param {string} 基本URL - API基本URL
 * @param {string} 用户名 - 用户名
 * @param {string} 设备令牌 - 设备令牌
 * @param {string} 设备名称 - 设备名称
 */
function 设置API配置(基本URL, 用户名, 设备令牌, 设备名称) {
    if (基本URL) API配置.基本URL = 基本URL;
    if (用户名) API配置.用户名 = 用户名;
    if (设备令牌) API配置.设备令牌 = 设备令牌;
    if (设备名称) API配置.设备名称 = 设备名称;
    console.log("API配置已更新: " + JSON.stringify(API配置));
}


/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;
    
    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");
        
        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }
        
        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}





/**
 * 获取互动元素（点赞、收藏、评论）
 * 通过查找Button元素的desc属性获取互动信息
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {Object|null} - 包含点赞、收藏、评论信息的对象，获取失败返回null
 */
function 获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };
        
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 定义普通互动区域（通常在屏幕下半部分）
        let 普通互动区域 = {
            left: 0,
            top: Math.floor(屏幕高度 * 0.5),  // 从屏幕中间开始
            right: 屏幕宽度,
            bottom: 屏幕高度
        };
        
        // 首先检查是否为视频页面 - 使用更高效的方法
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .find();
        
        let 是否视频页面 = 视频区域按钮.length > 0;
        结果.是否视频 = 是否视频页面;
        
        // 根据页面类型选择互动区域
        let 互动区域 = 是否视频页面 ? 视频互动区域 : 普通互动区域;
        
        // 使用更精确的选择器直接查找互动元素
        // 1. 查找点赞元素
        let 点赞元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*点赞.*")
            .findOne(500);
        
        if (点赞元素) {
            结果.点赞元素 = 点赞元素;
            let desc = 点赞元素.desc();
            结果.点赞 = 提取数字(desc);
        }
        
        // 2. 查找收藏元素
        let 收藏元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*收藏.*")
            .findOne(500);
        
        if (收藏元素) {
            结果.收藏元素 = 收藏元素;
            let desc = 收藏元素.desc();
            结果.收藏 = 提取数字(desc);
        }
        
        // 3. 查找评论元素
        let 评论元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*评论.*")
            .findOne(500);
        
        if (评论元素) {
            结果.评论元素 = 评论元素;
            let desc = 评论元素.desc();
            结果.评论 = 提取数字(desc);
        }
        
        // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 尝试在整个屏幕查找
            点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
            收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
            评论元素 = className("android.widget.Button").descMatches(".*评论.*").findOne(500);
            
            if (点赞元素) {
                结果.点赞元素 = 点赞元素;
                结果.点赞 = 提取数字(点赞元素.desc());
            }
            
            if (收藏元素) {
                结果.收藏元素 = 收藏元素;
                结果.收藏 = 提取数字(收藏元素.desc());
            }
            
            if (评论元素) {
                结果.评论元素 = 评论元素;
                结果.评论 = 提取数字(评论元素.desc());
            }
        }
        
        // 如果仍然没有找到互动元素，尝试使用text属性查找
        if (!结果.点赞元素 && !结果.收藏元素 && !结果.评论元素) {
            // 查找可能包含点赞、收藏、评论的文本
            let 点赞文本 = textMatches(".*点赞.*").findOne(300);
            let 收藏文本 = textMatches(".*收藏.*").findOne(300);
            let 评论文本 = textMatches(".*评论.*").findOne(300);
            
            if (点赞文本) {
                结果.点赞 = 提取数字(点赞文本.text());
            }
            
            if (收藏文本) {
                结果.收藏 = 提取数字(收藏文本.text());
            }
            
            if (评论文本) {
                结果.评论 = 提取数字(评论文本.text());
            }
        }
        
        // 视频页面特殊处理：如果没有找到点赞数，尝试查找纯数字文本
        if (是否视频页面 && 结果.点赞 === null) {
            let 可能的点赞数字 = textMatches("^\\d+$")
                .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
                .find();
            
            // 通常第一个纯数字是点赞数
            if (可能的点赞数字.length > 0) {
                for (let i = 0; i < 可能的点赞数字.length; i++) {
                    let 元素 = 可能的点赞数字[i];
                    let 文本 = 元素.text();
                    
                    // 尝试解析为数字
                    let 点赞数 = parseInt(文本);
                    if (!isNaN(点赞数)) {
                        结果.点赞 = 点赞数;
                        break;
                    }
                }
            }
        }
        
        // 检查是否找到任何互动元素
        if (结果.点赞 !== null || 结果.收藏 !== null || 结果.评论 !== null) {
            return 结果;
        }
        
        return null;
    } catch (e) {
        return null;
    }
}






/**
 * 全局变量，存储互动元素信息
 */
let 互动元素信息 = {
    点赞: null,
    收藏: null,
    评论: null,
    点赞元素: null,
    收藏元素: null,
    评论元素: null
};

/**
 * 等待指定时间
 * 
 * @param {number} 毫秒 - 等待的毫秒数
 */
function 等待(毫秒) {
    sleep(毫秒);
}

/**
 * 随机等待一段时间
 * 
 * @param {number} 最小毫秒 - 最小等待时间
 * @param {number} 最大毫秒 - 最大等待时间
 */
function 随机等待(最小毫秒, 最大毫秒) {
    let 等待时间 = 最小毫秒 + Math.floor(Math.random() * (最大毫秒 - 最小毫秒));
    console.log("随机等待 " + 等待时间 + " 毫秒");
    等待(等待时间);
}

/**
 * 处理权限弹窗
 * 检查并点击各种常见的权限按钮
 * 
 * @returns {boolean} - 是否找到并点击了权限按钮
 */
function 处理权限弹窗() {
    console.log("检查权限弹窗...");
    
    // 常见的权限按钮文本
    const 权限按钮文本列表 = ["允许", "始终允许", "确定", "继续", "同意", "确认", "好的"];
    
    // 遍历所有可能的按钮文本
    for (let i = 0; i < 权限按钮文本列表.length; i++) {
        let 按钮文本 = 权限按钮文本列表[i];
        console.log(`查找按钮: ${按钮文本}`);
        
        // 查找按钮
        let 按钮 = text(按钮文本).findOne(1000);
        if (按钮 && 按钮.clickable()) {
            console.log(`找到按钮 "${按钮文本}"，点击`);
            按钮.click();
            sleep(500);
            return true;
        }
    }
    
    // 如果没有找到可点击的文本按钮，尝试查找包含这些文本的任何元素
    for (let i = 0; i < 权限按钮文本列表.length; i++) {
        let 按钮文本 = 权限按钮文本列表[i];
        let 元素 = textContains(按钮文本).findOne(500);
        if (元素) {
            console.log(`找到包含 "${按钮文本}" 的元素，尝试点击`);
            let 成功 = 元素.click();
            if (!成功) {
                // 尝试点击元素中心位置
                let 位置 = 元素.bounds();
                if (位置) {
                    click(位置.centerX(), 位置.centerY());
                    console.log(`通过坐标点击: (${位置.centerX()}, ${位置.centerY()})`);
                }
            }
            sleep(500);
            return true;
        }
    }
    
    console.log("未找到权限弹窗");
    return false;
}

/**
 * 检查账号异常提示
 * 检测是否出现需要更换账号的提示，如账号下线、登录过期等
 * 优化版本：减少查询次数，提高执行效率
 * 
 * @returns {boolean} - 是否检测到账号异常提示
 */
function 检查账号异常提示() {
    console.log("检查账号异常提示...");
    
    try {
        // 一次性获取页面上所有文本元素，避免多次查询
        let 所有文本元素 = textMatches(".*").find();
        console.log(`找到 ${所有文本元素.length} 个文本元素`);
        
        // 常见的账号异常关键词
        const 异常关键词 = [
            "账号下线", "下线提示", "登录过期", "请重新登录", 
            "其他设备登录", "账号已被冻结", "账号异常", "违反社区规定"
        ];
        
        // 登录页面关键词
        const 登录页面关键词 = [
            "手机号登录", "用户协议", "隐私政策", "一键登录", "注册"
        ];
        
        // 检查所有文本元素是否包含异常关键词
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            // 检查账号异常关键词
            for (let j = 0; j < 异常关键词.length; j++) {
                if (文本内容.includes(异常关键词[j])) {
                    console.log(`检测到账号异常提示: "${文本内容}" 包含关键词 "${异常关键词[j]}"`);
                    return true;
                }
            }
            
            // 检查登录页面关键词
            for (let j = 0; j < 登录页面关键词.length; j++) {
                if (文本内容.includes(登录页面关键词[j])) {
                    console.log(`检测到登录页面: "${文本内容}" 包含关键词 "${登录页面关键词[j]}"`);
                    return true;
                }
            }
        }
        
        // 检查是否同时存在"小红书"和"生活指南"文本，这是登录页面的特征
        let 有小红书文本 = false;
        let 有生活指南文本 = false;
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容) continue;
            
            if (文本内容.includes("小红书")) {
                有小红书文本 = true;
            }
            if (文本内容.includes("生活指南")) {
                有生活指南文本 = true;
            }
            
            // 如果两者都找到了，可以提前返回
            if (有小红书文本 && 有生活指南文本) {
                console.log("检测到登录页面: 同时存在'小红书'和'生活指南'文本");
                return true;
            }
        }
        
        // 检查"知道了"按钮特殊情况
        let 知道了按钮 = false;
        for (let i = 0; i < 所有文本元素.length; i++) {
            if (所有文本元素[i].text() === "知道了") {
                知道了按钮 = true;
                break;
            }
        }
        
        if (知道了按钮) {
            console.log("检测到'知道了'按钮，检查是否有账号异常提示");
            
            // 再次遍历文本元素，查找可能的异常提示
            for (let i = 0; i < 所有文本元素.length; i++) {
                let 文本内容 = 所有文本元素[i].text();
                if (!文本内容 || 文本内容 === "知道了") continue;
                
                // 检查是否包含下线、登录、账号等关键词
                if (文本内容.includes("下线") || 
                    文本内容.includes("登录") || 
                    文本内容.includes("账号") ||
                    文本内容.includes("异常")) {
                    console.log(`检测到可能的账号异常提示: "${文本内容}"`);
                    return true;
                }
            }
        }
        
        // 如果上述检查都未发现异常，则认为没有账号异常
        console.log("未检测到账号异常提示");
        return false;
    } catch (e) {
        console.error("检查账号异常提示出错: " + e.message);
        // 出错时返回false，避免误判
        return false;
    }
}

// 在适当的位置调用检查账号异常提示函数
// 例如，可以在打开小红书、打开链接后调用

/**
 * 打开小红书应用
 */
function 打开小红书() {
    console.log("打开小红书应用");
    let 应用包名 = "com.xingin.xhs";
    
    try {
        app.launch(应用包名);
        等待(1000); // 等待应用启动
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        // 检查账号异常提示
        // if (检查账号异常提示()) {
        //     console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        //     toast("检测到账号异常，需要更换账号");
        //     return false; // 直接返回失败，中断操作
        // }
        
        console.log("小红书应用已启动");
        return true;
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 返回主界面
 * 多次按返回键，直到回到主界面
 * 优化版：减少查询次数和等待时间
 * 
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");
    
    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];
    
    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;
        
        try {
            // 查找所有文本元素，减少超时时间
            let 所有文本 = textMatches(".*").find();
            
            // 只记录找到的元素数量，不输出日志
            // console.log(`找到 ${所有文本.length} 个文本元素`);
            
            // 检查有多少关键词存在
            for (let j = 0; j < 所有文本.length; j++) {
                let 文本内容 = 所有文本[j].text();
                if (!文本内容) continue;
                
                // 使用some方法更高效地检查是否包含任何关键词
                if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                    找到的关键词++;
                    
                    // 减少日志输出
                    // console.log(`找到关键词: ${文本内容}`);
                    
                    // 如果找到3个或以上关键词，认为已在首页（降低标准以加快判断）
                    if (找到的关键词 >= 3) {
                        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                        return true;
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }
        
        // 按返回键
        console.log("按返回键");
        back();
        等待(1500);
    }
    
    // 最后检查一次
    let 找到的关键词 = 0;
    let 所有文本 = textMatches(".*").find();
    for (let j = 0; j < 所有文本.length; j++) {
        let 文本内容 = 所有文本[j].text();
        if (!文本内容) continue;
        
        if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
            找到的关键词++;
        }
    }
    
    if (找到的关键词 >= 5) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }
    
    console.log("未能确认返回首页");
    return false;
}

/**
 * 点击首篇文章
 * 
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");
    
    try {
        // 等待首页加载
        等待(2000);
        
        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20
        
        console.log(`点击坐标: (${x}, ${y})`);
        click(x, y);
        
        // 等待文章加载 - 减少等待时间
        等待(3000); // 从5000毫秒减少到3500毫秒
        
        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 打开链接(链接) {
    console.log("打开链接: " + 链接);
    
    try {
        // 解析链接获取笔记ID
/*         let noteId = 提取笔记ID(链接);
        if (!noteId) {
            console.error("无法从链接中提取笔记ID");
            return false;
        }
        
        console.log("提取到笔记ID: " + noteId);
        // 使用笔记ID打开小红书
        let 结果 = 小红书打开指定链接(noteId); */
        
        结果=在浏览器检测App内打开(链接)
        return 结果;
    } catch (e) {
        console.error("打开链接出错: " + e.message);
        return false;
    }
}


function 在浏览器检测App内打开(链接) {
    console.log("使用浏览器打开链接: " + 链接);
    app.openUrl(链接);
    toast("已用浏览器打开链接");
    
    // 最大尝试次数和超时时间
    const 最大尝试次数 = 5;
    const 每次尝试间隔 = 1000; // 2秒
    const 总超时时间 = 30000; // 30秒
    
    // 记录开始时间
    const 开始时间 = new Date().getTime();
    let 尝试次数 = 0;
    
    while (尝试次数 < 最大尝试次数 && (new Date().getTime() - 开始时间) < 总超时时间) {
        尝试次数++;
        console.log(`第 ${尝试次数} 次尝试打开小红书...`);
        
        // 等待页面加载
        console.log("等待页面加载...");
        sleep(每次尝试间隔);
        
        // 处理可能出现的权限弹窗
        let 权限处理结果 = 处理权限弹窗();
        if (权限处理结果) {
           // console.log("处理了权限弹窗，继续检查");
        }
        
        // 检查是否已经在小红书App中
        let pkg = typeof currentPackage === "function" ? currentPackage() : "";
        if (pkg === "com.xingin.xhs") {
            //console.log("已成功打开小红书App");
            return true;
        }
        
        // 检查是否有"App内打开"按钮
        console.log("检查是否有'App内打开'按钮...");
        let appButton = text('App内打开').findOne(2000);
        
        if (appButton) {
            console.log("找到'App内打开'按钮，点击");
            appButton.click();
            //toast("已点击'App内打开'按钮");
            
            // 等待小红书App启动
            sleep(1000);
            
            // 再次检查是否已经在小红书App中
            pkg = typeof currentPackage === "function" ? currentPackage() : "";
            if (pkg === "com.xingin.xhs") {
                //console.log("已成功打开小红书App");
                return true;
            }
        } else {
            //console.log("未找到'App内打开'按钮，检查其他可能的按钮...");
            
            // 尝试查找其他可能的按钮文本
            const 可能的按钮文本 = ["打开小红书", "打开APP", "在应用中打开", "打开应用"];
            
            for (let i = 0; i < 可能的按钮文本.length; i++) {
                let 按钮文本 = 可能的按钮文本[i];
                let 其他按钮 = text(按钮文本).findOne(1000);
                
                if (其他按钮) {
                    console.log(`找到'${按钮文本}'按钮，点击`);
                    其他按钮.click();
                    sleep(500);
                    break;
                }
            }
        }
    }
    
    // 检查最终结果
    let pkg = typeof currentPackage === "function" ? currentPackage() : "";
    if (pkg === "com.xingin.xhs") {
        //console.log("最终确认：已成功打开小红书App");
        return true;
    } else {
        //console.log(`尝试 ${尝试次数} 次后未能自动打开小红书App`);
        toast("请手动点击'App内打开'按钮");
        return false;
    }
}



/**
 * 直接通过ID打开小红书笔记
 * 
 * @param {string} noteId - 笔记ID
 * @returns {boolean} - 是否成功打开
 */
function 小红书打开指定链接(noteId) {
    try {
        console.log("使用ID打开小红书: " + noteId);
        
        // 构建深度链接并打开
        app.startActivity({
            action: "VIEW",
            data: "xhsdiscover://item/" + noteId
        });
        
        console.log("已发送打开请求");
        sleep(3000); // 等待应用打开
        
        // 处理可能出现的权限弹窗
        处理权限弹窗();
        
        // 检查账号异常提示
        // if (检查账号异常提示()) {
        //     console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        //     toast("检测到账号异常，需要更换账号");
        //     return false; // 直接返回失败，中断操作
        // }
        
        return true;
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 从URL中提取笔记ID
 * 支持短链接、长链接和直接的笔记ID
 * 
 * @param {string} url - 小红书URL或笔记ID
 * @returns {string|null} - 笔记ID，失败返回null
 */
function 提取笔记ID(url) {
    try {
        console.log("开始提取笔记ID...");
        
        // 确保url是字符串类型
        url = String(url);
        
        // 如果链接为空
        if (!url) {
            console.error("无效的链接");
            return null;
        }
        
        // 情况1: 直接是笔记ID
        if (/^[a-zA-Z0-9_]+$/.test(url) && !url.includes(".")) {
            console.log("检测到笔记ID，直接返回");
            return url;
        }
        
        // 情况2: 小红书长链接
        if (url.indexOf("xiaohongshu.com") !== -1) {
            console.log("检测到小红书长链接，提取ID");
            
            // 直接尝试匹配ID模式（通常是24位字符）
            let idMatch = url.match(/([a-zA-Z0-9]{24})/);
            if (idMatch && idMatch[1]) {
                console.log("通过ID模式匹配成功: " + idMatch[1]);
                return idMatch[1];
            }
            
            // 尝试提取 /explore/[noteId]
            let match = url.match(/\/explore\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                console.log("通过explore路径匹配成功: " + match[1]);
                return match[1];
            }
            
            // 尝试提取 /discovery/item/[noteId]
            match = url.match(/\/discovery\/item\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                // 如果ID包含参数，只取问号前面的部分
                let id = match[1].split('?')[0];
                console.log("通过discovery路径匹配成功: " + id);
                return id;
            }
            
            console.error("未能从长链接提取笔记ID");
            return null;
        }
        
        // 情况3: 小红书短链接，需要先解析
        if (url.indexOf("xhslink.com") !== -1) {
            console.log("检测到小红书短链接，开始解析...");
            
            try {
                // 发送HTTP请求获取重定向URL
                let response = http.get(url, {
                    followRedirect: true,  // 自动跟随重定向
                    headers: {
                        "User-Agent": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36"
                    }
                });
                
                // 从最终URL中提取ID
                let finalUrl = response.url;
                console.log("短链接解析结果: " + finalUrl);
                
                // 再次调用提取笔记ID函数处理解析后的URL
                return 提取笔记ID(finalUrl);
            } catch (e) {
                console.error("解析短链接失败: " + e.message);
                return null;
            }
        }
        
        // 尝试直接从URL中提取24位ID格式
        let idMatch = url.match(/([a-zA-Z0-9]{24})/);
        if (idMatch && idMatch[1]) {
            console.log("从URL中直接提取到ID: " + idMatch[1]);
            return idMatch[1];
        }
        
        // 情况4: 其他未识别的链接类型
        console.error("未识别的链接类型，无法提取ID: " + url);
        return null;
    } catch (e) {
        console.error("提取笔记ID出错: " + e.message);
        return null;
    }
}

/**
 * 执行点赞操作
 * 
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作");
    
    try {
        if (!互动元素信息.点赞元素) {
            console.log("未找到点赞元素");
            return false;
        }
        
        // 检查是否已点赞
        let desc = 互动元素信息.点赞元素.desc();
        if (desc && desc.includes("已点赞")) {
            console.log("已经点过赞了");
            return true;
        }
        
        // 点击点赞按钮
        console.log("点击点赞按钮");
        互动元素信息.点赞元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return false;
    }
}

/**
 * 执行收藏操作
 * 
 * @returns {boolean} - 是否成功收藏
 */
function 执行收藏() {
    console.log("执行收藏操作");
    
    try {
        if (!互动元素信息.收藏元素) {
            console.log("未找到收藏元素");
            return false;
        }
        
        // 检查是否已收藏
        let desc = 互动元素信息.收藏元素.desc();
        if (desc && desc.includes("已收藏")) {
            console.log("已经收藏过了");
            return true;
        }
        
        // 点击收藏按钮
        console.log("点击收藏按钮");
        互动元素信息.收藏元素.click();
        等待(1000);
        
        return true;
    } catch (e) {
        console.error("执行收藏出错: " + e.message);
        return false;
    }
}

/**
 * 清除存储的设备令牌
 * 强制重新生成设备令牌
 */
function 清除设备令牌() {
    console.log("清除存储的设备令牌");
    storages.create("小红书操作").remove("设备令牌");
    API配置.设备令牌 = "";
    是否首次运行 = true;
}

/**
 * 获取特定配置项的值
 * 
 * @param {string} 配置名称 - 配置项名称
 * @param {any} 默认值 - 如果配置不存在，返回的默认值
 * @returns {any} - 配置项的值
 */
function 获取配置项(配置名称, 默认值) {
    // 先尝试直接从存储中获取
    let storage = storages.create("小红书操作");
    let 配置值 = storage.get(配置名称);
    
    // 如果直接存储中有值，直接返回
    if (配置值 !== undefined && 配置值 !== null) {
        console.log(`获取配置项 ${配置名称}: ${配置值}`);
        return 配置值;
    }
    
    // 尝试从所有配置中获取
    let 所有配置 = storage.get("所有配置");
    if (所有配置) {
        // 遍历所有配置项
        for (let 配置组名 in 所有配置) {
            let 配置组 = 所有配置[配置组名];
            // 如果配置组中包含指定的配置项
            if (配置组 && 配置组[配置名称] !== undefined) {
                console.log(`从配置组 ${配置组名} 中获取配置项 ${配置名称}: ${配置组[配置名称]}`);
                return 配置组[配置名称];
            }
        }
    }
    
    // 尝试从当前配置中获取
    let 当前配置 = storage.get("当前配置");
    if (当前配置 && 当前配置.config && 当前配置.config[配置名称] !== undefined) {
        console.log(`从当前配置中获取配置项 ${配置名称}: ${当前配置.config[配置名称]}`);
        return 当前配置.config[配置名称];
    }
    
    // 如果都没有找到，返回默认值
    console.log(`未找到配置项 ${配置名称}，使用默认值: ${默认值}`);
    return 默认值;
}

/**
 * 主循环操作函数
 * 根据剩余操作次数循环执行操作，直到达到最大操作次数
 */
function 开始小红书点赞操作() {
    console.log("开始循环操作");
    
    // 清除存储的设备令牌，强制重新生成
    清除设备令牌();
    
    // 确保首次运行标志为true，生成新的设备令牌
    是否首次运行 = true;
    
    // 注册设备
    console.log("注册设备");
    let 注册结果 = 设备注册();
    if (!注册结果.success) {
        console.error("设备注册失败: " + 注册结果.message);
        toast("设备注册失败: " + 注册结果.message);
        // 如果是达到上限，直接return
        if (注册结果.limitReached) {
            return { success: false, message: "今日操作次数已达上限" };
        }
        return { success: false, message: "设备注册失败: " + 注册结果.message };
    }
    
    // 获取操作信息
    let 操作信息 = 获取操作信息();
    console.log("当前操作信息: " + JSON.stringify(操作信息));
    
    // 检查剩余操作次数，如果为0则直接退出
    if (操作信息.剩余操作次数 <= 0) {
        console.log("当前设备今日操作次数已达上限，无法继续操作");
        toast("今日操作次数已达上限，即将退出本次循环");
        sleep(3000);
        return { success: false, message: "今日操作次数已达上限" };
    }
    
    // 获取"操作几次恢复出厂设置"配置
    let 操作几次恢复出厂设置 = 获取配置项("操作几次恢复出厂设置", 0);
    console.log(`配置项: 操作几次恢复出厂设置 = ${操作几次恢复出厂设置}`);
    
    // 打开小红书应用
    if (!打开小红书()) {
        toast("打开小红书应用失败");
        return { success: false, message: "打开小红书应用失败" };
    }
    
    // 检查是否出现账号异常提示
    // if (检查账号异常提示()) {
    //     console.log("检测到账号异常提示，需要更换账号，结束当前操作");
    //     toast("检测到账号异常，需要更换账号");
    //     return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
    // }
    
    // 初始化计数器
    let 已完成操作次数 = 0;
    let 剩余操作次数 = 操作信息.剩余操作次数 || 操作信息.最大操作次数;
    
    console.log(`开始循环操作，剩余操作次数: ${剩余操作次数}`);
    toast(`开始循环操作，剩余操作次数: ${剩余操作次数}`);
    
    // 主循环
    while (已完成操作次数 < 剩余操作次数) {
        // 检查是否需要恢复出厂设置
        if (操作几次恢复出厂设置 > 0 && 已完成操作次数 > 0 && 已完成操作次数 % 操作几次恢复出厂设置 === 0) {
            console.log(`已完成 ${已完成操作次数} 次操作，达到恢复出厂设置条件（每 ${操作几次恢复出厂设置} 次）`);
            console.log(`准备恢复出厂设置 (${已完成操作次数}次)`);
            toast(`已完成 ${已完成操作次数} 次操作，准备恢复出厂设置`);
            
            // 调用设备操作模块的恢复出厂设置功能
            try {
                if (typeof deviceOp !== 'undefined' && typeof deviceOp.恢复出厂设置 === 'function') {
                    //console.log("调用设备操作模块恢复出厂设置");
                    deviceOp.恢复出厂设置();
                    
                    // 恢复出厂设置后，需要重新注册设备并重启循环
                    //console.log("恢复出厂设置完成，重新开始循环操作");
                    //toast("恢复出厂设置完成，重新开始循环操作");
                    
                    // 延迟一段时间后重新开始
                    //sleep(10000);
                    开始小红书点赞操作();
                    return { success: true, message: "已恢复出厂设置并重新开始循环操作" };
                } else {
                    console.log("设备操作模块不可用或不支持恢复出厂设置功能");
                }
            } catch (e) {
                console.error("尝试恢复出厂设置时出错: " + e.message);
            }
        }
        
        // 获取链接
        console.log(`[${已完成操作次数+1}/${剩余操作次数}] 获取链接中...`);
        let 获取链接结果 = 获取下一个链接();
        
        // 如果获取链接失败或没有链接可用，等待一段时间后重试
        if (!获取链接结果.success || 获取链接结果.status !== "success") {
            // 如果是达到上限，直接return
            if (获取链接结果.message && 获取链接结果.message.indexOf("上限") !== -1) {
                return { success: false, message: "今日操作次数已达上限" };
            }
            console.log("获取链接失败或暂无链接，等待30秒后重试");
            toast("暂无链接，等待中...");
            
            // 等待30秒
            for (let i = 30; i > 0; i--) {
                if (i % 5 === 0) { // 每5秒输出一次日志和提示
                    console.log(`暂无链接，等待中...(${i}秒)`);
                    toast(`暂无链接，等待中...(${i}秒)`);
                }
                sleep(1000);
            }
            
            continue; // 继续下一次循环
        }
        
        // 执行智能自动操作
        console.log(`[${已完成操作次数+1}/${剩余操作次数}] 执行操作`);
        let 操作结果 = 智能自动操作(0); // 不指定最大操作次数，使用注册获取的值
        
        // 检查是否因为账号异常而需要结束操作
        if (操作结果.needChangeAccount) {
            console.log("检测到账号异常，需要更换账号，结束当前循环操作");
            toast("检测到账号异常，需要更换账号，结束操作");
            return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
        }
        
        // 无论成功失败，都计数为已完成一次操作
        已完成操作次数++;
        
        // 更新操作信息，检查是否已达到上限
        操作信息 = 获取操作信息();
        操作信息.剩余操作次数=剩余操作次数-已完成操作次数
        console.log("当前操作信息,剩余操作次数 : " + 操作信息.剩余操作次数);
        if (操作信息.剩余操作次数 <= 0) {
            console.log("已达到每日操作上限，准备退出本次循环");
            toast("今日操作次数已达上限，即将退出本次循环");
            sleep(3000);
            return { success: true, message: "已达到每日操作上限，脚本已退出" };
        }
        
        // 更新悬浮窗
        if (操作结果.success) {
            console.log(`[${已完成操作次数}/${剩余操作次数}] 操作成功`);
            toast(`操作成功 [${已完成操作次数}/${剩余操作次数}]`);
        } else {
            console.log(`[${已完成操作次数}/${剩余操作次数}] 操作失败`);
            toast(`操作失败 [${已完成操作次数}/${剩余操作次数}]`);
        }
        
        // 随机等待一段时间，避免操作过于频繁
        //let 等待时间 = 3000 + Math.floor(Math.random() * 2000); // 3-5秒
        //console.log(`随机等待 ${Math.floor(等待时间/1000)} 秒`);
        //toast(`等待 ${Math.floor(等待时间/1000)} 秒后继续`);
        //sleep(等待时间);
    }
    
    // 循环结束，清理注册信息，确保下次重新注册
    console.log("循环操作完成，清理注册信息");
    清除设备令牌();
    storages.create("小红书操作").remove("设备ID");
    storages.create("小红书操作").remove("操作信息");
    是否首次运行 = true;
    
    console.log(`循环操作完成，共完成 ${已完成操作次数} 次操作`);
    toast(`循环操作完成，共完成 ${已完成操作次数} 次操作`);
    return { success: true, message: `循环操作完成，共完成 ${已完成操作次数} 次操作` };
}

/**
 * 智能自动操作
 * 完整的自动化流程，包括注册设备、获取链接、打开链接、获取互动元素信息、
 * 返回主界面、点击首篇文章、执行点赞/收藏操作、更新操作状态
 * 
 * @param {number} 最大操作次数 - 最大操作次数，默认为0（由注册获取）
 * @returns {Object} - 操作结果
 */
function 智能自动操作(最大操作次数 = 0) {
    console.log("开始智能自动操作");
    toast("开始智能自动操作");
    
    // 不再清除设备令牌，使用已注册的设备令牌
    // 清除设备令牌();
    
    // 确保首次运行标志为false，避免重新生成设备令牌
    是否首次运行 = false;
    
    // 获取操作信息
    let 操作信息 = 获取操作信息();
    //console.log("当前操作信息: " + JSON.stringify(操作信息));
    
    // 如果指定了最大操作次数，则使用指定的值
    if (最大操作次数 > 0) {
        操作信息.最大操作次数 = 最大操作次数;
    }
    
    // 首先获取一个链接，并保存分配信息
    console.log("预先获取链接，建立分配关系");
    let 初始链接结果 = 获取下一个链接();
    if (!初始链接结果.success) {
        console.error("预先获取链接失败: " + 初始链接结果.message);
        toast("预先获取链接失败");
        return { success: false, message: "预先获取链接失败: " + 初始链接结果.message };
    }
    
    // 保存初始链接的分配信息
    let 初始链接 = 初始链接结果.data.url;
    let 初始链接ID = 初始链接结果.data.link_id;
    
    console.log(`已获取链接 - 链接ID: ${初始链接ID}`);
    toast(`已获取链接，准备操作`);
    
    // 保存到存储
    let storage = storages.create("小红书操作");
    storage.put("当前链接ID", 初始链接ID);
    
    // 记录服务器返回的数据，用于后续提交状态和比较
    let 原始点赞数 = 初始链接结果.data.original_likes || 0;
    let 原始收藏数 = 初始链接结果.data.original_collects || 0;
    let 目标点赞数 = 初始链接结果.data.target_likes || 0;
    let 目标收藏数 = 初始链接结果.data.target_collects || 0;
    let 当前点赞数 = 初始链接结果.data.current_likes || 0;
    let 当前收藏数 = 初始链接结果.data.current_collects || 0;
    let 实际点赞增加数 = 初始链接结果.data.actual_like_increase || 0;
    let 实际收藏增加数 = 初始链接结果.data.actual_collect_increase || 0;
    
    //console.log(`服务器记录的数据 - 原始点赞: ${原始点赞数}, 原始收藏: ${原始收藏数}`);
    //console.log(`服务器记录的数据 - 当前点赞: ${当前点赞数}, 当前收藏: ${当前收藏数}`);
    //console.log(`服务器记录的数据 - 目标点赞: ${目标点赞数}, 目标收藏: ${目标收藏数}`);
    //console.log(`服务器记录的数据 - 实际点赞增加: ${实际点赞增加数}, 实际收藏增加: ${实际收藏增加数}`);
    
    // 计算还需要增加的点赞/收藏数
    let 需要增加点赞数 = 目标点赞数 - 实际点赞增加数;
    let 需要增加收藏数 = 目标收藏数 - 实际收藏增加数;
    
    // 处理初始值为0的特殊情况
    // 当原始值为0时，可能是服务端还未获取到实际初始值，此时忽略实际增加数的计算
    if (原始点赞数 === 0) {
        console.log("原始点赞数为0，忽略实际增加数计算，直接使用目标点赞数");
        需要增加点赞数 = 目标点赞数;
    }
    
    if (原始收藏数 === 0) {
        console.log("原始收藏数为0，忽略实际增加数计算，直接使用目标收藏数");
        需要增加收藏数 = 目标收藏数;
    }
    
    console.log(`还需要增加 - 点赞: ${需要增加点赞数}, 收藏: ${需要增加收藏数}`);
    
    // 检查是否已经达到目标
    let 点赞已达标 = 需要增加点赞数 <= 0;
    let 收藏已达标 = 需要增加收藏数 <= 0;
    
    // 如果点赞和收藏都已达标，则直接返回
    if (点赞已达标 && 收藏已达标) {
        console.log("点赞和收藏均已达到目标数量，无需操作");
        toast("点赞和收藏均已达标，无需操作");
        更新操作状态(初始链接ID, "success", "both", 当前点赞数, 当前收藏数);
        return { success: true, message: "点赞和收藏均已达到目标数量，无需操作" };
    }
    if (检查账号异常提示()) {
        console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        toast("检测到账号异常，需要更换账号");
        更新操作状态(初始链接ID, "failed", "both", 当前点赞数, 当前收藏数, "账号异常，需要更换账号");
        return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
    }
    
    // 打开初始链接，并获取页面信息
    console.log("打开初始链接获取信息");
    toast("正在打开链接...");
    if (!打开链接(初始链接)) {
        console.error("打开初始链接失败");
        toast("打开链接失败");
        
        // 检查是否是因为账号异常而失败
        if (检查账号异常提示()) {
            console.log("检测到账号异常提示，需要更换账号，结束当前操作");
            toast("检测到账号异常，需要更换账号");
            更新操作状态(初始链接ID, "failed", "both", 当前点赞数, 当前收藏数, "账号异常，需要更换账号");
            return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
        }
        
        更新操作状态(初始链接ID, "failed", "both", 当前点赞数, 当前收藏数, "打开链接失败");
        return { success: false, message: "打开初始链接失败" };
    }
    
    // 检查是否出现账号异常提示
    if (检查账号异常提示()) {
        console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        toast("检测到账号异常，需要更换账号");
        更新操作状态(初始链接ID, "failed", "both", 当前点赞数, 当前收藏数, "账号异常，需要更换账号");
        return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
    }
    
    // 使用新的获取页面信息函数获取初始链接的信息
    let 初始链接页面信息 = 获取页面信息();
    console.log("获取到初始链接页面信息");
    
    // 如果获取到了互动元素信息，记录初始数据
    let 初始链接点赞数 = 初始链接页面信息.点赞数;
    let 初始链接收藏数 = 初始链接页面信息.收藏数;
    
    if (初始链接点赞数 !== null || 初始链接收藏数 !== null) {
        console.log(`初始链接实际数据 - 点赞: ${初始链接点赞数}, 收藏: ${初始链接收藏数}`);
        
        // 使用页面实际数据更新当前数量
        if (初始链接点赞数 !== null) {
            当前点赞数 = 初始链接点赞数;
            // 重新计算实际增加数和需要增加数
            实际点赞增加数 = 当前点赞数 - 原始点赞数;
            需要增加点赞数 = 目标点赞数 - 实际点赞增加数;
            
            // 处理初始值为0的特殊情况
            if (原始点赞数 === 0) {
                console.log("原始点赞数为0，忽略实际增加数计算，直接使用目标点赞数");
                需要增加点赞数 = 目标点赞数;
            }
            
            console.log(`更新后的数据 - 当前点赞: ${当前点赞数}, 实际点赞增加: ${实际点赞增加数}, 还需增加: ${需要增加点赞数}`);
        }
        
        if (初始链接收藏数 !== null) {
            当前收藏数 = 初始链接收藏数;
            // 重新计算实际增加数和需要增加数
            实际收藏增加数 = 当前收藏数 - 原始收藏数;
            需要增加收藏数 = 目标收藏数 - 实际收藏增加数;
            
            // 处理初始值为0的特殊情况
            if (原始收藏数 === 0) {
                console.log("原始收藏数为0，忽略实际增加数计算，直接使用目标收藏数");
                需要增加收藏数 = 目标收藏数;
            }
            
            console.log(`更新后的数据 - 当前收藏: ${当前收藏数}, 实际收藏增加: ${实际收藏增加数}, 还需增加: ${需要增加收藏数}`);
        }
        
        // 重新检查是否已达标
        点赞已达标 = 需要增加点赞数 <= 0;
        收藏已达标 = 需要增加收藏数 <= 0;
        
        // 如果点赞和收藏都已达标，则直接返回
        if (点赞已达标 && 收藏已达标) {
            console.log("根据初始链接数据，点赞和收藏均已达到目标数量，但仍需完成完整流程");
            // 不立即返回，继续执行后续流程以验证页面信息
        }
    }
    
    // 减少等待时间，快速返回首页
    //等待(500);
    
    // 返回首页
    toast("返回首页...");
    if (!返回主界面()) {
        console.log("无法返回首页，更新操作状态为失败");
        toast("无法返回首页");
        更新操作状态(初始链接ID, "failed", "both", 初始链接点赞数, 初始链接收藏数, "无法返回首页");
        return { success: false, message: "无法返回首页" };
    }
    
    // 减少随机等待时间
    //随机等待(1000, 2000);
    
    // 点击首页第一篇文章
    toast("点击首页文章...");
    if (!点击首篇文章()) {
        console.log("点击首篇文章失败，更新操作状态为失败");
        toast("点击首页文章失败");
        更新操作状态(初始链接ID, "failed", "both", 初始链接点赞数, 初始链接收藏数, "点击首篇文章失败");
        return { success: false, message: "点击首篇文章失败" };
    }
    
    // 使用新的获取页面信息函数获取首页文章的信息
    let 首页文章页面信息 = 获取页面信息();
    console.log("获取到首页文章页面信息");
    
    // 检查是否获取到页面信息
    if (!首页文章页面信息 || (!首页文章页面信息.点赞数 && !首页文章页面信息.收藏数)) {
        console.log("未获取到首页文章页面信息");
        toast("未获取到页面信息");
        更新操作状态(初始链接ID, "failed", "both", 初始链接点赞数, 初始链接收藏数, "未获取到页面信息");
        返回主界面();
        return { success: false, message: "未获取到页面信息" };
    }
    
    console.log("获取到首页文章页面信息: " + JSON.stringify({
        标题: 首页文章页面信息.标题,
        用户名: 首页文章页面信息.用户名,
        点赞数: 首页文章页面信息.点赞数,
        收藏数: 首页文章页面信息.收藏数,
        评论数: 首页文章页面信息.评论数,
        是否视频: 首页文章页面信息.是否视频
    }));
    
    // 使用比较页面信息函数判断是否为同一篇文章
    let 是同一篇文章 = 比较页面信息(初始链接页面信息, 首页文章页面信息);
    
    // 如果不是同一篇文章，使用首页文章的数据更新互动元素信息
    if (!是同一篇文章) {
        console.log("首页文章与初始链接不是同一篇文章，使用首页文章数据");
        console.log("不是同一篇文章，提交失败状态");
        toast("首页文章与初始链接不是同一篇文章");
        // 只使用初始链接的数据提交失败状态
        更新操作状态(初始链接ID, "failed", "both", 初始链接点赞数, 初始链接收藏数, "首页文章与初始链接不是同一篇文章");
        返回主界面();
        return { success: false, message: "首页文章与初始链接不是同一篇文章" };
    } else {
        console.log("首页文章与初始链接是同一篇文章，继续使用初始数据");
        // 使用最新获取的互动信息更新互动元素信息
        互动元素信息 = 首页文章页面信息.原始元素.互动元素 || 获取互动元素();
    }
    
    // 如果初始检查已确定点赞和收藏都已达标，直接更新状态并返回
    if (点赞已达标 && 收藏已达标) {
        console.log("点赞和收藏均已达到目标数量，无需操作");
        toast("点赞和收藏均已达标，无需操作");
        更新操作状态(初始链接ID, "success", "both", 初始链接点赞数, 初始链接收藏数);
        返回主界面();
        return { success: true, message: "点赞和收藏均已达到目标数量，无需操作" };
    }
    
    // 根据是否达标决定是否执行操作
    let 点赞成功 = false;
    let 收藏成功 = false;
    
    if (!点赞已达标) {
        toast("执行点赞...");
        点赞成功 = 执行点赞();
        随机等待(500, 1000);
    } else {
        console.log("点赞已达标，跳过点赞操作");
    }
    
    if (!收藏已达标) {
        toast("执行收藏...");
        收藏成功 = 执行收藏();
    } else {
        console.log("收藏已达标，跳过收藏操作");
    }
    
    // 确定操作类型
    let 操作类型 = "both";
    if (点赞成功 && !收藏成功) {
        操作类型 = "like";
    } else if (!点赞成功 && 收藏成功) {
        操作类型 = "collect";
    } else if (!点赞成功 && !收藏成功) {
        // 如果都没有执行操作，检查是否因为已达标
        if (点赞已达标 && 收藏已达标) {
            console.log("点赞和收藏均已达标，无需操作");
            toast("点赞和收藏均已达标，无需操作");
            更新操作状态(初始链接ID, "success", "both", 初始链接点赞数, 初始链接收藏数);
            返回主界面();
            return { success: true, message: "点赞和收藏均已达标，无需操作" };
        }
    }
    
    // 立即更新操作状态 - 使用当前实际数据
    if (点赞成功 || 收藏成功) {
        console.log("操作成功，更新状态");
        toast("操作成功，更新状态");
        let 更新结果 = 更新操作状态(初始链接ID, "success", 操作类型, 初始链接点赞数, 初始链接收藏数);
        console.log("更新状态结果: " + JSON.stringify(更新结果));
    } else {
        console.log("操作失败，更新状态");
        toast("操作失败，更新状态");
        更新操作状态(初始链接ID, "failed", "both", 初始链接点赞数, 初始链接收藏数, "点赞和收藏操作均失败");
    }
    
    // 返回主界面
    返回主界面();
    
    console.log("智能自动操作完成");
    toast("智能自动操作完成");
    return { success: true, message: "智能自动操作完成" };
}


/**
 * 获取作者信息
 * 通过查找作者元素获取作者名称
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {string|null} - 作者名称，获取失败返回null
 */
function 获取作者信息() {
    try {
        // 查找包含"作者"的Button元素
        let 作者元素 = className("android.widget.Button").descMatches(".*作者.*").findOne(300);
        
        if (作者元素) {
            let desc = 作者元素.desc();
            
            // 从描述中提取作者名称
            let 作者名称匹配 = desc.match(/作者,(.+)/);
            if (作者名称匹配 && 作者名称匹配[1]) {
                let 作者名称 = 作者名称匹配[1].trim();
                return 作者名称;
            }
        }
        
        // 尝试其他方式查找作者信息
        // 1. 查找包含@的文本
        let 包含艾特的文本 = textMatches("@.*").findOne(300);
        if (包含艾特的文本) {
            return 包含艾特的文本.text();
        }
        
        // 2. 查找可能的作者名称（短文本，通常在页面上方）
        let 屏幕高度 = device.height;
        let 可能的作者元素列表 = className("android.widget.TextView")
            .boundsInside(0, 0, device.width, Math.floor(屏幕高度 * 0.3))
            .find();
        
        // 筛选可能的作者名称
        for (let i = 0; i < 可能的作者元素列表.length; i++) {
            let 元素 = 可能的作者元素列表[i];
            let 文本 = 元素.text();
            
            // 作者名称通常较短，且不包含特殊字符
            if (文本 && 文本.length > 0 && 文本.length < 20 && 
                !文本.includes("点赞") && !文本.includes("收藏") && 
                !文本.includes("评论") && !文本.match(/^\d+$/)) {
                return 文本;
            }
        }
        
        return null;
    } catch (e) {
        return null;
    }
}


/**
 * 测试API连接和功能
 * 
 * @returns {Object} - 测试结果
 */
function 测试API连接() {
    console.log("开始API连接测试");
    
    // 测试设备注册
    console.log("1. 测试设备注册");
    let 设备令牌 = 生成设备令牌();
    API配置.设备令牌 = 设备令牌;
    
    let 注册结果 = 设备注册();
    console.log("设备注册结果: " + JSON.stringify(注册结果));
    
    if (!注册结果.success) {
        return { success: false, message: "设备注册失败", data: 注册结果 };
    }
    
    // 测试获取链接
    console.log("2. 测试获取链接");
    let 链接结果 = 获取下一个链接();
    console.log("获取链接结果: " + JSON.stringify(链接结果));
    
    if (!链接结果.success || 链接结果.status !== "success") {
        return { success: false, message: "获取链接失败", data: 链接结果 };
    }
    
    let 链接ID = 链接结果.data.link_id;
    
    // 测试更新操作状态
    console.log("3. 测试更新操作状态");
    let 更新结果 = 更新操作状态(链接ID, "success", "like", 100, 50);
    console.log("更新操作状态结果: " + JSON.stringify(更新结果));
    
    if (!更新结果.success) {
        return { success: false, message: "更新操作状态失败", data: 更新结果 };
    }
    
    // 全部测试通过
    return { 
        success: true, 
        message: "API连接测试通过", 
        data: {
            设备令牌: 设备令牌,
            注册结果: 注册结果,
            链接结果: 链接结果,
            更新结果: 更新结果
        }
    };
}

// 为AutoJS环境导出函数
// if (typeof module !== 'undefined') {
//     module.exports = {
//         设备注册: 设备注册,
//         获取下一个链接: 获取下一个链接,
//         更新操作状态: 更新操作状态,
//         获取作信息: 获取操作信息,
//         获取设备令牌: 获取设备令牌,
//         更新设备令牌: 更新设备令牌,
//         清除设备令牌: 清除设备令牌,
//         设置API配置: 设置API配置,
//         获取互动元素: 获取互动元素,
//         提取数字: 提取数字,
//         API配置: API配置,
//         智能自动操作: 智能自动操作,
//         获取当前配置: 获取当前配置,
//         开始小红书点赞操作: 开始小红书点赞操作
//     };
// }

// 正确导出流程函数
module.exports = {
    开始小红书点赞操作,
    设备注册,
    获取当前配置,
    // 添加新函数到导出列表
    获取页面信息,
    获取页面文本内容,
    判断是否为视频页面,
    比较页面信息,
    获取互动元素,
    获取作者信息,
    提取数字
};

// 直接调用循环操作函数进行测试
//开始小红书点赞操作()

// 导出测试函数
global.测试API连接 = 测试API连接;
global.开始小红书点赞操作 = 开始小红书点赞操作;

// 读取手机脚本目录下的 config.txt 配置
function 读取配置文件(路径) {
    var 配置 = {};
    try {
        var file = new java.io.File(路径);
        if (!file.exists()) {
            console.error("配置文件不存在: " + 路径);
            return 配置;
        }
        var fis = new java.io.FileInputStream(file);
        var isr = new java.io.InputStreamReader(fis, "utf-8");
        var br = new java.io.BufferedReader(isr);
        var line;
        while ((line = br.readLine()) != null) {
            line = line.trim();
            if (line === '' || line.startsWith('#')) continue;
            var idx = line.indexOf('=');
            if (idx > 0) {
                var key = line.substring(0, idx).trim();
                var value = line.substring(idx + 1).trim();
                配置[key] = value;
            }
        }
        br.close();
        isr.close();
        fis.close();
    } catch (e) {
        console.error("读取配置文件出错: " + e);
    }
    return 配置;
}

// 读取配置
var 配置路径 = "/sdcard/脚本/config.txt";
var 配置项 = (typeof 读取配置文件 === 'function') ? 读取配置文件(配置路径) : (function(){
    var 配置 = {};
    try {
        var file = new java.io.File(配置路径);
        if (!file.exists()) {
            console.error("配置文件不存在: " + 配置路径);
            return 配置;
        }
        var fis = new java.io.FileInputStream(file);
        var isr = new java.io.InputStreamReader(fis, "utf-8");
        var br = new java.io.BufferedReader(isr);
        var line;
        while ((line = br.readLine()) != null) {
            line = line.trim();
            if (line === '' || line.startsWith('#')) continue;
            var idx = line.indexOf('=');
            if (idx > 0) {
                var key = line.substring(0, idx).trim();
                var value = line.substring(idx + 1).trim();
                配置[key] = value;
            }
        }
        br.close();
        isr.close();
        fis.close();
    } catch (e) {
        console.error("读取配置文件出错: " + e);
    }
    return 配置;
})();

// 如果配置文件有内容则覆盖默认，否则用默认
if (配置项.基本URL) API配置.基本URL = 配置项.基本URL;
if (配置项.用户名) API配置.用户名 = 配置项.用户名;
console.log("最终使用的API配置：" + JSON.stringify(API配置));

/**
 * 获取页面文本内容
 * 获取页面上所有有效的文本内容，按长度排序
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {Array} - 包含文本内容、长度和元素的数组，按长度降序排序
 */
function 获取页面文本内容() {
    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 获取页面所有文本元素 - 使用更高效的选择器
        let 所有文本元素 = className("android.widget.TextView")
            .boundsInside(0, 0, 屏幕宽度, 屏幕高度)
            .find();
        
        // 创建一个数组存储所有有效文本及其长度
        let 有效文本列表 = [];
        
        // 要过滤的关键词
        const 过滤关键词 = ["点赞", "收藏", "评论", "小红书", "分享", "复制链接", "举报"];
        
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 元素 = 所有文本元素[i];
            let 文本 = 元素.text();
            
            // 跳过空文本或无效文本
            if (!文本 || 文本.length < 2) continue;
            
            // 跳过纯数字文本
            if (文本.match(/^\d+$/)) continue;
            
            // 跳过包含特定关键词的文本
            let 包含关键词 = false;
            for (let j = 0; j < 过滤关键词.length; j++) {
                if (文本.includes(过滤关键词[j])) {
                    包含关键词 = true;
                    break;
                }
            }
            if (包含关键词) continue;
            
            // 获取元素位置
            let 元素位置 = 元素.bounds();
            
            // 检查元素是否在屏幕可见区域内
            let 是否在屏幕内 = 元素位置.top >= 0 && 元素位置.bottom <= 屏幕高度;
            if (!是否在屏幕内) continue;
            
            // 将有效文本添加到列表
            有效文本列表.push({
                文本: 文本,
                长度: 文本.length,
                元素: 元素
            });
        }
        
        // 按文本长度降序排序
        有效文本列表.sort((a, b) => b.长度 - a.长度);
        
        return 有效文本列表;
    } catch (e) {
        return [];
    }
}

/**
 * 判断是否为视频页面
 * 通过检查屏幕右侧区域是否有互动按钮来判断
 * 优化版本：提高查询效率
 * 
 * @returns {boolean} - 是否为视频页面
 */
function 判断是否为视频页面() {
    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;
        
        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };
        
        // 视频页面通常右侧有特定的互动按钮 - 使用findOne提高效率
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .findOne(300);
        
        // 如果在视频互动区域找到按钮，很可能是视频页面
        return 视频区域按钮 != null;
    } catch (e) {
        return false;
    }
}

/**
 * 获取页面信息
 * 获取当前页面的标题、内容、用户名等信息
 * 优化版本：提高查询效率，减少日志输出
 * 
 * @returns {Object} - 包含页面信息的对象
 */
function 获取页面信息() {
    try {
        // 初始化结果对象
        let 结果 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            是否视频: false,
            链接ID: null,
            原始元素: {}
        };
        
        // 1. 获取互动信息（点赞、收藏、评论）
        let 互动信息 = 获取互动元素();
        if (互动信息) {
            结果.点赞数 = 互动信息.点赞;
            结果.收藏数 = 互动信息.收藏;
            结果.评论数 = 互动信息.评论;
            结果.原始元素.互动元素 = 互动信息;
            
            // 使用互动元素函数返回的是否视频标志
            if (互动信息.是否视频 !== undefined) {
                结果.是否视频 = 互动信息.是否视频;
            } else {
                // 如果互动元素函数没有返回是否视频标志，使用传统方法判断
                if (互动信息.点赞元素) {
                    let desc = 互动信息.点赞元素.desc();
                    if (desc) {
                        // 视频的点赞元素描述通常没有空格，如"点赞3671"
                        // 图文的点赞元素描述通常有空格，如"点赞 1445"
                        结果.是否视频 = !desc.includes(" ");
                    }
                }
            }
        } else {
            // 如果获取互动信息失败，尝试直接判断是否为视频页面
            结果.是否视频 = 判断是否为视频页面();
        }
        
        // 2. 获取用户名/作者信息
        结果.用户名 = 获取作者信息();
        
        // 3. 获取页面文本内容
        let 有效文本列表 = 获取页面文本内容();
        
        // 处理视频页面的特殊情况
        if (结果.是否视频) {
            // 对于视频页面，用户名通常是最短的有效文本之一
            if (有效文本列表.length > 0) {
                // 如果还没有获取到用户名，使用最短的有效文本
                if (!结果.用户名 && 有效文本列表.length > 0) {
                    let 最短文本 = 有效文本列表[有效文本列表.length - 1].文本;
                    结果.用户名 = 最短文本;
                }
                
                // 视频标题可能是最长的文本
                if (有效文本列表.length >= 1) {
                    let 可能的标题 = 有效文本列表[0].文本;
                    结果.标题 = 可能的标题;
                    结果.原始元素.标题元素 = 有效文本列表[0].元素;
                    
                    // 视频内容使用最长文本，但限制在50个字符以内
                    if (可能的标题.length > 50) {
                        结果.内容 = 可能的标题.substring(0, 50) + "...";
                    } else {
                        结果.内容 = 可能的标题;
                    }
                }
            }
        } else {
            // 非视频页面（图文）的处理逻辑
            
            // 标题通常是较长的文本，但不是最长的
            if (有效文本列表.length > 0) {
                // 使用最长的文本作为内容
                let 最长文本 = 有效文本列表[0].文本;
                结果.内容 = 最长文本.length > 50 ? 最长文本.substring(0, 50) + "..." : 最长文本;
                结果.原始元素.内容元素 = 有效文本列表[0].元素;
                
                // 如果有第二长的文本，使用它作为标题
                if (有效文本列表.length > 1) {
                    结果.标题 = 有效文本列表[1].文本;
                    结果.原始元素.标题元素 = 有效文本列表[1].元素;
                }
            }
        }
        
        return 结果;
    } catch (e) {
        return {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            是否视频: false,
            链接ID: null,
            原始元素: {},
            success: false,
            error: e.message
        };
    }
}

/**
 * 比较两个页面信息，判断是否为同一篇文章
 * 优化版本：提高比较效率，减少日志输出
 * 
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} - 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    // 如果任一信息为空，返回false
    if (!信息1 || !信息2) return false;
    
    // 1. 检查作者是否相同
    let 作者相同 = 信息1.用户名 && 信息2.用户名 && 信息1.用户名 === 信息2.用户名;
    
    // 2. 检查内容是否相似（如果内容存在）
    let 内容相似 = false;
    if (信息1.内容 && 信息2.内容) {
        // 如果内容长度超过20个字符，比较前20个字符是否相同
        if (信息1.内容.length > 20 && 信息2.内容.length > 20) {
            内容相似 = 信息1.内容.substring(0, 20) === 信息2.内容.substring(0, 20);
        } else {
            // 否则比较整个内容
            内容相似 = 信息1.内容 === 信息2.内容;
        }
    }
    
    // 3. 检查点赞数和收藏数是否在合理范围内
    let 点赞数合理 = false;
    let 收藏数合理 = false;
    
    if (信息1.点赞数 !== null && 信息2.点赞数 !== null) {
        // 允许点赞数有小幅增长（其他用户可能点赞）或小幅减少（最多减少5个，可能是其他用户取消点赞）
        let 点赞差值 = 信息2.点赞数 - 信息1.点赞数;
        点赞数合理 = 点赞差值 >= -5 && 点赞差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    if (信息1.收藏数 !== null && 信息2.收藏数 !== null) {
        // 允许收藏数有小幅增长或小幅减少
        let 收藏差值 = 信息2.收藏数 - 信息1.收藏数;
        收藏数合理 = 收藏差值 >= -5 && 收藏差值 < 10; // 允许减少最多5个，增加不超过10个
    }
    
    // 4. 综合判断是否是同一篇文章
    // 如果作者相同且内容相似，几乎可以确定是同一篇文章
    if (作者相同 && 内容相似) {
        return true;
    }
    
    // 如果作者相同且点赞数和收藏数变化合理，可能是同一篇文章
    if (作者相同 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    // 如果内容相似且点赞数和收藏数变化合理，可能是同一篇文章
    if (内容相似 && 点赞数合理 && 收藏数合理) {
        return true;
    }
    
    return false;
}

/**
 * 显示性能优化总结
 * 在脚本启动时显示性能优化信息
 */
function 显示性能优化总结() {
    console.log("========== 小红书自动点赞脚本性能优化总结 ==========");
    console.log("1. 元素查找优化");
    console.log("   - 使用更精确的选择器，减少查找次数");
    console.log("   - 限制元素查找范围，只在可能区域内查找");
    console.log("   - 使用findOne替代find，提高查询效率");
    console.log("   - 优先使用descMatches而非遍历所有元素");
    
    console.log("2. 页面信息获取优化");
    console.log("   - 一次性获取所有文本元素，避免多次查询");
    console.log("   - 使用缓存避免重复获取相同信息");
    console.log("   - 优化文本过滤算法，提高筛选效率");
    console.log("   - 区分视频页面和图文页面的处理逻辑");
    
    console.log("3. 日志输出优化");
    console.log("   - 减少不必要的日志输出，降低I/O开销");
    console.log("   - 仅保留关键信息的输出，提高执行效率");
    
    console.log("4. 异常处理优化");
    console.log("   - 简化try-catch结构，减少嵌套层级");
    console.log("   - 统一错误处理逻辑，提高代码稳定性");
    
    console.log("5. 页面比较优化");
    console.log("   - 使用更高效的页面信息比较算法");
    console.log("   - 优先比较关键特征，快速判断页面相似性");
    
    console.log("=================================================");
}

// 在模块加载时显示优化信息
// 显示性能优化总结();

