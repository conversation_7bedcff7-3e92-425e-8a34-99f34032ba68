/**
 * 简单的小红书启动测试
 * 直接运行这个脚本来测试启动命令
 */

console.log("开始测试小红书启动...");

// 测试monkey命令（最推荐）
console.log("测试1: monkey命令");
let result1 = shell("monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1", true);
console.log("monkey结果:", result1);

if (result1.code === 0) {
    console.log("✅ monkey命令成功！");
    console.log("建议在你的脚本中使用:");
    console.log('let result = shell("monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1", true);');
} else {
    console.log("❌ monkey命令失败，尝试其他方法...");
    
    // 测试am start命令
    console.log("\n测试2: am start命令");
    let result2 = shell("am start com.xingin.xhs", true);
    console.log("am start结果:", result2);
    
    if (result2.code === 0) {
        console.log("✅ am start命令成功！");
        console.log("建议在你的脚本中使用:");
        console.log('let result = shell("am start com.xingin.xhs", true);');
    } else {
        console.log("❌ am start也失败了");
        
        // 测试带Activity的命令
        console.log("\n测试3: 带Activity的命令");
        let result3 = shell("am start -n com.xingin.xhs/.ui.activity.SplashActivity", true);
        console.log("带Activity结果:", result3);
        
        if (result3.code === 0) {
            console.log("✅ 带Activity命令成功！");
            console.log("建议在你的脚本中使用:");
            console.log('let result = shell("am start -n com.xingin.xhs/.ui.activity.SplashActivity", true);');
        } else {
            console.log("❌ 所有测试都失败了");
            console.log("可能的原因:");
            console.log("1. 没有root权限");
            console.log("2. 小红书未安装");
            console.log("3. 小红书版本不同，Activity名称变化");
        }
    }
}

console.log("\n测试完成！");
