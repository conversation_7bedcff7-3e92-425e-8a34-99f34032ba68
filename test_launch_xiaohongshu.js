/**
 * 小红书启动测试脚本
 * 在AutoJS中运行此脚本来找到正确的启动命令
 */

console.log("=== 小红书启动测试 ===");

// 测试启动命令列表
let 启动命令列表 = [
    "monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1",
    "am start -n com.xingin.xhs/.ui.activity.SplashActivity", 
    "am start -n com.xingin.xhs/.activity.SplashActivity",
    "am start -n com.xingin.xhs/.MainActivity",
    "am start -n com.xingin.xhs/.LauncherActivity",
    "am start -n com.xingin.xhs/.SplashActivity",
    "am start com.xingin.xhs",
    "am start -a android.intent.action.MAIN -c android.intent.category.LAUNCHER com.xingin.xhs"
];

function 测试启动命令() {
    console.log("开始测试各种启动命令...\n");
    
    for (let i = 0; i < 启动命令列表.length; i++) {
        let 命令 = 启动命令列表[i];
        console.log(`测试 ${i + 1}: ${命令}`);
        
        try {
            let result = shell(命令, true);
            console.log(`返回码: ${result.code}`);
            
            if (result.code === 0) {
                console.log("✅ 命令执行成功！");
                console.log("等待3秒检查应用是否启动...");
                sleep(3000);
                
                // 检查小红书是否真的启动了
                let checkResult = shell("dumpsys window | grep mCurrentFocus", true);
                if (checkResult.code === 0 && checkResult.result.includes("com.xingin.xhs")) {
                    console.log("🎉 小红书启动成功！");
                    console.log(`推荐使用命令: ${命令}`);
                    toast("找到可用命令: " + 命令);
                    return 命令;
                } else {
                    console.log("⚠️ 命令成功但应用未启动");
                }
            } else {
                console.log(`❌ 命令失败: ${result.error}`);
            }
        } catch (e) {
            console.log(`❌ 异常: ${e.message}`);
        }
        
        console.log("---");
        sleep(1000); // 等待1秒再测试下一个
    }
    
    console.log("❌ 所有命令都失败了");
    return null;
}

// 检查小红书是否安装
function 检查安装() {
    console.log("检查小红书是否安装...");
    
    try {
        let result = shell("pm list packages | grep com.xingin.xhs", true);
        if (result.code === 0 && result.result.includes("com.xingin.xhs")) {
            console.log("✅ 小红书已安装");
            return true;
        } else {
            console.log("❌ 小红书未安装");
            return false;
        }
    } catch (e) {
        console.log("检查失败: " + e.message);
        return false;
    }
}

// 主函数
function main() {
    if (!检查安装()) {
        toast("请先安装小红书应用");
        return;
    }
    
    let 成功命令 = 测试启动命令();
    
    if (成功命令) {
        console.log(`\n🎉 测试完成！可用命令: ${成功命令}`);
        console.log("请将此命令复制到你的脚本中使用");
    } else {
        console.log("\n❌ 未找到可用的启动命令");
        console.log("可能需要:");
        console.log("1. 确保有root权限");
        console.log("2. 检查小红书版本");
        console.log("3. 尝试手动启动小红书后查看Activity名称");
    }
}

// 运行测试
main();
