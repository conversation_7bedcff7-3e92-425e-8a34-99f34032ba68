/**
 * 测试小红书启动命令
 * 用于找到正确的启动方式
 */

console.log("开始测试小红书启动命令...");

// 测试不同的启动方式
function 测试启动命令() {
    let 测试方法 = [
        {
            名称: "使用monkey命令启动",
            命令: "monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1"
        },
        {
            名称: "使用am start启动默认Activity",
            命令: "am start com.xingin.xhs"
        },
        {
            名称: "使用am start启动包名",
            命令: "am start -n com.xingin.xhs"
        },
        {
            名称: "使用am start启动MainActivity",
            命令: "am start -n com.xingin.xhs/.MainActivity"
        },
        {
            名称: "使用am start启动LauncherActivity",
            命令: "am start -n com.xingin.xhs/.LauncherActivity"
        },
        {
            名称: "使用am start启动SplashActivity",
            命令: "am start -n com.xingin.xhs/.SplashActivity"
        }
    ];

    console.log("开始测试各种启动方式...");
    
    for (let i = 0; i < 测试方法.length; i++) {
        let 方法 = 测试方法[i];
        console.log(`\n测试方法 ${i + 1}: ${方法.名称}`);
        console.log(`命令: ${方法.命令}`);
        
        try {
            let result = shell(方法.命令, true);
            console.log(`结果码: ${result.code}`);
            console.log(`输出: ${result.result}`);
            console.log(`错误: ${result.error}`);
            
            if (result.code === 0) {
                console.log(`✅ 方法 ${i + 1} 成功！`);
                console.log("等待3秒检查是否启动成功...");
                sleep(3000);
                
                // 检查小红书是否启动
                let checkResult = shell("dumpsys window | grep mCurrentFocus", true);
                if (checkResult.code === 0 && checkResult.result.includes("com.xingin.xhs")) {
                    console.log("✅ 小红书已成功启动！");
                    console.log(`推荐使用命令: ${方法.命令}`);
                    return 方法.命令;
                } else {
                    console.log("❌ 命令执行成功但小红书未启动");
                }
            } else {
                console.log(`❌ 方法 ${i + 1} 失败`);
            }
        } catch (e) {
            console.log(`❌ 方法 ${i + 1} 异常: ${e.message}`);
        }
        
        console.log("---");
    }
    
    return null;
}

// 查询小红书的Activity信息
function 查询小红书Activity() {
    console.log("\n查询小红书的Activity信息...");
    
    try {
        let result = shell("dumpsys package com.xingin.xhs | grep -A 5 -B 5 Activity", true);
        console.log("Activity查询结果:");
        console.log(result.result);
    } catch (e) {
        console.log("查询Activity失败: " + e.message);
    }
    
    try {
        let result2 = shell("pm dump com.xingin.xhs | grep -i activity", true);
        console.log("\nActivity详细信息:");
        console.log(result2.result);
    } catch (e) {
        console.log("查询Activity详细信息失败: " + e.message);
    }
}

// 检查小红书是否安装
function 检查小红书安装() {
    console.log("检查小红书是否安装...");
    
    try {
        let result = shell("pm list packages | grep com.xingin.xhs", true);
        if (result.code === 0 && result.result.includes("com.xingin.xhs")) {
            console.log("✅ 小红书已安装");
            console.log("包信息: " + result.result);
            return true;
        } else {
            console.log("❌ 小红书未安装");
            return false;
        }
    } catch (e) {
        console.log("检查安装状态失败: " + e.message);
        return false;
    }
}

// 主函数
function main() {
    console.log("=== 小红书启动测试工具 ===\n");
    
    // 1. 检查安装状态
    if (!检查小红书安装()) {
        console.log("请先安装小红书应用");
        return;
    }
    
    // 2. 查询Activity信息
    查询小红书Activity();
    
    // 3. 测试启动命令
    let 成功命令 = 测试启动命令();
    
    if (成功命令) {
        console.log(`\n🎉 找到可用的启动命令: ${成功命令}`);
        console.log("你可以在脚本中使用这个命令来启动小红书");
    } else {
        console.log("\n❌ 所有测试方法都失败了");
        console.log("可能的原因:");
        console.log("1. 小红书版本不同，Activity名称发生变化");
        console.log("2. 需要root权限");
        console.log("3. 系统限制");
    }
}

// 运行测试
main();
